<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TrackingController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\AddressController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

// Frontend routes
Route::get('/', [\App\Http\Controllers\FrontendController::class, 'home'])->name('home');
Route::get('/about', [\App\Http\Controllers\FrontendController::class, 'about'])->name('about');
Route::get('/services', [\App\Http\Controllers\FrontendController::class, 'services'])->name('services');
Route::get('/contact', [\App\Http\Controllers\FrontendController::class, 'contact'])->name('contact');
Route::post('/contact', [\App\Http\Controllers\FrontendController::class, 'submitContact'])->name('contact.submit');

// Legal pages
Route::get('/terms-of-service', [\App\Http\Controllers\FrontendController::class, 'termsOfService'])->name('terms-of-service');
Route::get('/privacy-policy', [\App\Http\Controllers\FrontendController::class, 'privacyPolicy'])->name('privacy-policy');

// Newsletter routes
Route::post('/newsletter/subscribe', [\App\Http\Controllers\FrontendController::class, 'subscribeNewsletter'])->name('newsletter.subscribe');
Route::get('/newsletter/unsubscribe/{token}', [\App\Http\Controllers\FrontendController::class, 'unsubscribeNewsletter'])->name('newsletter.unsubscribe');

// Career Routes
Route::prefix('careers')->name('careers.')->group(function () {
    Route::get('/', [\App\Http\Controllers\CareerController::class, 'index'])->name('index');
    Route::get('/filter', [\App\Http\Controllers\CareerController::class, 'filterByDepartment'])->name('filter');
    Route::get('/{career:slug}', [\App\Http\Controllers\CareerController::class, 'show'])->name('show');
    Route::get('/{career:slug}/apply', [\App\Http\Controllers\CareerController::class, 'apply'])->name('apply');
    Route::post('/{career:slug}/apply', [\App\Http\Controllers\CareerController::class, 'storeApplication'])->name('apply.store');
});

// SEO and Sitemap routes
Route::get('/sitemap.xml', [\App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap.index');
Route::get('/sitemap-main.xml', [\App\Http\Controllers\SitemapController::class, 'main'])->name('sitemap.main');
Route::get('/sitemap-{locale}.xml', [\App\Http\Controllers\SitemapController::class, 'locale'])->name('sitemap.locale');
Route::get('/robots.txt', [\App\Http\Controllers\SeoController::class, 'robots'])->name('robots');

// Live Chat routes (public API)
Route::prefix('api/live-chat')->name('live-chat.')->group(function () {
    Route::post('start-session', [\App\Http\Controllers\LiveChatController::class, 'startSession'])->name('start-session');
    Route::post('send-message', [\App\Http\Controllers\LiveChatController::class, 'sendMessage'])->name('send-message');
    Route::get('messages/{sessionId}', [\App\Http\Controllers\LiveChatController::class, 'getMessages'])->name('get-messages');
    Route::get('check-new-messages/{sessionId}', [\App\Http\Controllers\LiveChatController::class, 'checkNewMessages'])->name('check-new-messages');
    Route::post('end-session/{sessionId}', [\App\Http\Controllers\LiveChatController::class, 'endSession'])->name('end-session');
});

// API endpoint for testing
Route::get('/api-test', function () {
    return response()->json([
        'message' => 'Atrix Logistics API is working!',
        'timestamp' => now(),
        'database' => config('database.connections.mysql.database'),
        'parcels_count' => \App\Models\Parcel::count(),
        'sample_parcel' => \App\Models\Parcel::with('carrier')->first(),
    ]);
});

Route::get('/test', function () {
    return 'Laravel is working with MySQL database: ' . config('database.connections.mysql.database');
});

// Tracking routes
Route::get('/track', [TrackingController::class, 'index'])->name('tracking.index');
Route::get('/track/search', [TrackingController::class, 'track'])->name('tracking.search');
Route::post('/track', [TrackingController::class, 'track'])->name('tracking.track');

// Public product routes (no authentication required)
Route::get('/products', [\App\Http\Controllers\ProductController::class, 'index'])->name('products.index');
Route::get('/products/{product:slug}', [\App\Http\Controllers\ProductController::class, 'show'])->name('products.show');
Route::get('/categories/{category:slug}', [\App\Http\Controllers\CategoryController::class, 'show'])->name('categories.show');

// Blog routes
Route::get('/blog', [\App\Http\Controllers\BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{blogPost}', [\App\Http\Controllers\BlogController::class, 'show'])->name('blog.show');
Route::get('/blog/feed/rss', [\App\Http\Controllers\BlogController::class, 'rss'])->name('blog.rss');
Route::get('/blog/sitemap.xml', [\App\Http\Controllers\BlogController::class, 'sitemap'])->name('blog.sitemap');

// Public quote submission (no authentication required)
Route::post('/quotes', [\App\Http\Controllers\QuoteController::class, 'store'])->name('quotes.store');

// Health check routes
Route::get('/health', [\App\Http\Controllers\HealthController::class, 'check'])->name('health.check');
Route::get('/health/detailed', [\App\Http\Controllers\HealthController::class, 'detailed'])->name('health.detailed');
Route::get('/health/system', [\App\Http\Controllers\HealthController::class, 'system'])->name('health.system');
Route::post('/health/metrics', [\App\Http\Controllers\HealthController::class, 'metrics'])->name('health.metrics');

// OTP Authentication routes
Route::prefix('auth/otp')->name('auth.otp.')->group(function () {
    Route::post('/send', [\App\Http\Controllers\Auth\OtpController::class, 'sendOtp'])->name('send');
    Route::post('/verify', [\App\Http\Controllers\Auth\OtpController::class, 'verifyOtp'])->name('verify');
    Route::post('/resend', [\App\Http\Controllers\Auth\OtpController::class, 'resendOtp'])->name('resend');
    Route::get('/status', [\App\Http\Controllers\Auth\OtpController::class, 'checkOtpStatus'])->name('status');
})->middleware(['guest', 'throttle:10,1']); // 10 requests per minute

// Document Generation routes (Protected)
Route::prefix('documents')->name('documents.')->middleware(['auth'])->group(function () {
    // PDF Generation
    Route::get('/invoice/{order}/generate', [\App\Http\Controllers\DocumentController::class, 'generateInvoice'])->name('invoice.generate');
    Route::get('/quote/{quote}/generate', [\App\Http\Controllers\DocumentController::class, 'generateQuote'])->name('quote.generate');
    Route::get('/waybill/{parcel}/generate', [\App\Http\Controllers\DocumentController::class, 'generateWaybill'])->name('waybill.generate');
    Route::get('/shipping-label/{parcel}/generate', [\App\Http\Controllers\DocumentController::class, 'generateShippingLabel'])->name('shipping_label.generate');
    Route::get('/delivery-receipt/{parcel}/generate', [\App\Http\Controllers\DocumentController::class, 'generateDeliveryReceipt'])->name('delivery_receipt.generate');

    // Preview (HTML)
    Route::get('/invoice/{order}/preview', [\App\Http\Controllers\DocumentController::class, 'previewInvoice'])->name('invoice.preview');
    Route::get('/quote/{quote}/preview', [\App\Http\Controllers\DocumentController::class, 'previewQuote'])->name('quote.preview');
    Route::get('/waybill/{parcel}/preview', [\App\Http\Controllers\DocumentController::class, 'previewWaybill'])->name('waybill.preview');

    // Bulk Generation
    Route::post('/bulk-generate', [\App\Http\Controllers\DocumentController::class, 'bulkGenerate'])->name('bulk.generate');
});

// API routes
Route::prefix('api')->group(function () {
    Route::get('/track/{trackingNumber}', [TrackingController::class, 'apiTrack'])->name('api.track');
    Route::get('/products', [\App\Http\Controllers\ProductController::class, 'apiIndex'])->name('api.products');


})->middleware(['api.rate.limit:120,1']); // 120 requests per minute

// Customer Authentication Routes
Route::prefix('customer')->name('customer.')->group(function () {
    // Guest routes (not authenticated)
    Route::middleware('guest')->group(function () {
        Route::get('register', [\App\Http\Controllers\Customer\AuthController::class, 'showRegister'])->name('register');
        Route::post('register', [\App\Http\Controllers\Customer\AuthController::class, 'register']);
        Route::get('login', [\App\Http\Controllers\Customer\AuthController::class, 'showLogin'])->name('login');
        Route::post('login', [\App\Http\Controllers\Customer\AuthController::class, 'login']);
        Route::post('login/phone', [\App\Http\Controllers\Customer\AuthController::class, 'loginWithPhone'])->name('login.phone');
        Route::get('forgot-password', [\App\Http\Controllers\Customer\AuthController::class, 'showForgotPassword'])->name('forgot-password');
        Route::post('forgot-password', [\App\Http\Controllers\Customer\AuthController::class, 'forgotPassword']);
        Route::get('password/request', [\App\Http\Controllers\Customer\AuthController::class, 'showForgotPassword'])->name('password.request');
        Route::post('password/email', [\App\Http\Controllers\Customer\AuthController::class, 'forgotPassword'])->name('password.email');

        // Customer OTP routes
        Route::prefix('otp')->name('otp.')->group(function () {
            Route::post('/send', [\App\Http\Controllers\Customer\OtpController::class, 'sendOtp'])->name('send');
            Route::post('/verify', [\App\Http\Controllers\Customer\OtpController::class, 'verifyOtp'])->name('verify');
        })->middleware('throttle:10,1');
    });

    // Authenticated customer routes
    Route::middleware(['auth', 'customer'])->group(function () {
        // Dashboard
        Route::get('dashboard', [\App\Http\Controllers\Customer\DashboardController::class, 'index'])->name('dashboard');

        // Parcels
        Route::get('parcels', [\App\Http\Controllers\Customer\DashboardController::class, 'parcels'])->name('parcels');
        Route::get('parcels/{parcel}', [\App\Http\Controllers\Customer\DashboardController::class, 'parcelDetails'])->name('parcels.show');

        // Tracking
        Route::get('track', [\App\Http\Controllers\Customer\DashboardController::class, 'trackParcel'])->name('track');

        // Orders
        Route::get('orders', [\App\Http\Controllers\Customer\DashboardController::class, 'orders'])->name('orders');
        Route::get('orders/{order}', [\App\Http\Controllers\Customer\DashboardController::class, 'orderDetails'])->name('orders.show');

        // Order Payments
        Route::get('orders/{order}/pay', [\App\Http\Controllers\Customer\PaymentController::class, 'showOrderPayment'])->name('orders.pay');
        Route::post('orders/{order}/pay', [\App\Http\Controllers\Customer\PaymentController::class, 'processOrderPayment'])->name('orders.pay.process');
        Route::get('orders/{order}/payment/paypal/return', [\App\Http\Controllers\Customer\PaymentController::class, 'orderPaypalReturn'])->name('orders.payment.paypal.return');
        Route::get('orders/{order}/payment/paypal/cancel', [\App\Http\Controllers\Customer\PaymentController::class, 'orderPaypalCancel'])->name('orders.payment.paypal.cancel');

        // Order Actions
        Route::post('orders/{order}/cancel', [\App\Http\Controllers\Customer\DashboardController::class, 'cancelOrder'])->name('orders.cancel');
        Route::post('orders/{order}/reorder', [\App\Http\Controllers\Customer\DashboardController::class, 'reorderItems'])->name('orders.reorder');

        // Wishlist
        Route::prefix('wishlist')->name('wishlist.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Customer\WishlistController::class, 'index'])->name('index');
            Route::post('add', [\App\Http\Controllers\Customer\WishlistController::class, 'store'])->name('add');
            Route::delete('remove', [\App\Http\Controllers\Customer\WishlistController::class, 'destroy'])->name('remove');
            Route::delete('{wishlist}', [\App\Http\Controllers\Customer\WishlistController::class, 'remove'])->name('remove-item');
            Route::put('{wishlist}/notes', [\App\Http\Controllers\Customer\WishlistController::class, 'updateNotes'])->name('update-notes');
            Route::post('{wishlist}/add-to-cart', [\App\Http\Controllers\Customer\WishlistController::class, 'addToCart'])->name('add-to-cart');
            Route::post('{wishlist}/move-to-cart', [\App\Http\Controllers\Customer\WishlistController::class, 'moveToCart'])->name('move-to-cart');
            Route::get('count', [\App\Http\Controllers\Customer\WishlistController::class, 'count'])->name('count');
            Route::get('check', [\App\Http\Controllers\Customer\WishlistController::class, 'check'])->name('check');
            Route::delete('clear', [\App\Http\Controllers\Customer\WishlistController::class, 'clear'])->name('clear');
        });

        // Support Tickets
        Route::prefix('support')->name('support.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Customer\SupportController::class, 'index'])->name('index');
            Route::get('create', [\App\Http\Controllers\Customer\SupportController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Customer\SupportController::class, 'store'])->name('store');
            Route::get('{ticket}', [\App\Http\Controllers\Customer\SupportController::class, 'show'])->name('show');
            Route::get('{ticket}/edit', [\App\Http\Controllers\Customer\SupportController::class, 'edit'])->name('edit');
            Route::put('{ticket}', [\App\Http\Controllers\Customer\SupportController::class, 'update'])->name('update');
            Route::post('{ticket}/close', [\App\Http\Controllers\Customer\SupportController::class, 'close'])->name('close');
            Route::post('{ticket}/reopen', [\App\Http\Controllers\Customer\SupportController::class, 'reopen'])->name('reopen');
        });

        // Payment System
        Route::prefix('payments')->name('payments.')->group(function () {
            Route::get('{parcel}', [\App\Http\Controllers\Customer\PaymentController::class, 'show'])->name('show');
            Route::post('{parcel}/process', [\App\Http\Controllers\Customer\PaymentController::class, 'process'])->name('process');
            Route::get('{payment}/success', [\App\Http\Controllers\Customer\PaymentController::class, 'success'])->name('success');

            // PayPal specific routes
            Route::get('{parcel}/paypal/return', [\App\Http\Controllers\Customer\PaymentController::class, 'paypalReturn'])->name('paypal.return');
            Route::get('{parcel}/paypal/cancel', [\App\Http\Controllers\Customer\PaymentController::class, 'paypalCancel'])->name('paypal.cancel');
        });

        // Quote Requests
        Route::prefix('quotes')->name('quotes.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Customer\QuoteController::class, 'index'])->name('index');
            Route::get('create', [\App\Http\Controllers\Customer\QuoteController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Customer\QuoteController::class, 'store'])->name('store');
            Route::get('lookup', [\App\Http\Controllers\Customer\QuoteController::class, 'lookup'])->name('lookup');
            Route::post('lookup', [\App\Http\Controllers\Customer\QuoteController::class, 'lookupQuote'])->name('lookup.search');
            Route::get('{quote}', [\App\Http\Controllers\Customer\QuoteController::class, 'show'])->name('show');
            Route::post('{quote}/accept', [\App\Http\Controllers\Customer\QuoteController::class, 'accept'])->name('accept');
            Route::post('{quote}/reject', [\App\Http\Controllers\Customer\QuoteController::class, 'reject'])->name('reject');
            Route::post('{quote}/notes', [\App\Http\Controllers\Customer\QuoteController::class, 'addNotes'])->name('add-notes');
        });

        // Profile Management
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Customer\ProfileController::class, 'show'])->name('show');
            Route::get('edit', [\App\Http\Controllers\Customer\ProfileController::class, 'edit'])->name('edit');
            Route::put('update', [\App\Http\Controllers\Customer\ProfileController::class, 'update'])->name('update');

            Route::get('change-password', [\App\Http\Controllers\Customer\ProfileController::class, 'showChangePassword'])->name('change-password');
            Route::put('update-password', [\App\Http\Controllers\Customer\ProfileController::class, 'updatePassword'])->name('update-password');

            Route::get('preferences', [\App\Http\Controllers\Customer\ProfileController::class, 'showPreferences'])->name('preferences');
            Route::put('preferences', [\App\Http\Controllers\Customer\ProfileController::class, 'updatePreferences'])->name('update-preferences');

            Route::get('address-book', [\App\Http\Controllers\Customer\ProfileController::class, 'addressBook'])->name('address-book');

            Route::delete('delete', [\App\Http\Controllers\Customer\ProfileController::class, 'destroy'])->name('delete');
        });

        // Logout
        Route::post('logout', [\App\Http\Controllers\Customer\AuthController::class, 'logout'])->name('logout');
    });
});

// Default dashboard redirect to admin for admin users
Route::get('/dashboard', function () {
    if (auth()->check() && in_array(auth()->user()->role, ['admin', 'staff'])) {
        return redirect()->route('admin.dashboard');
    }
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics-data', [\App\Http\Controllers\Admin\DashboardController::class, 'analyticsData'])->name('analytics-data');

    // Global Search
    Route::get('/search', [\App\Http\Controllers\Admin\GlobalSearchController::class, 'search'])->name('search');
    Route::resource('parcels', \App\Http\Controllers\Admin\ParcelController::class);

    // Parcel bulk operations
    Route::post('parcels/bulk-update', [\App\Http\Controllers\Admin\ParcelController::class, 'bulkUpdate'])->name('parcels.bulk-update');
    Route::post('parcels/{parcel}/send-notification', [\App\Http\Controllers\Admin\ParcelController::class, 'sendNotification'])->name('parcels.send-notification');

    Route::resource('parcels.invoices', App\Http\Controllers\Admin\InvoiceController::class)->shallow()->except(['edit', 'update']);
    // Adding a specific route for creating an invoice for a parcel
    Route::get('parcels/{parcel}/invoices/create', [App\Http\Controllers\Admin\InvoiceController::class, 'create'])->name('parcels.invoices.create');
    Route::get('invoices/{invoice}/download', [App\Http\Controllers\Admin\InvoiceController::class, 'downloadPdf'])->name('invoices.download');
    Route::get('invoices', [App\Http\Controllers\Admin\InvoiceController::class, 'indexAll'])->name('invoices.index.all'); // To list all invoices

    // Tracking events
    Route::post('tracking-events', [\App\Http\Controllers\Admin\TrackingEventController::class, 'store'])->name('tracking-events.store');
    Route::put('tracking-events/{trackingEvent}', [\App\Http\Controllers\Admin\TrackingEventController::class, 'update'])->name('tracking-events.update');
    Route::delete('tracking-events/{trackingEvent}', [\App\Http\Controllers\Admin\TrackingEventController::class, 'destroy'])->name('tracking-events.destroy');
    Route::get('parcels/{parcel}/events', [\App\Http\Controllers\Admin\TrackingEventController::class, 'getEvents'])->name('parcels.events');

    // Reports
    Route::get('reports', [\App\Http\Controllers\Admin\ReportController::class, 'index'])->name('reports.index');
    Route::get('reports/parcels-analytics', [\App\Http\Controllers\Admin\ReportController::class, 'parcelsAnalytics'])->name('reports.parcels-analytics');
    Route::get('reports/customer-analytics', [\App\Http\Controllers\Admin\ReportController::class, 'customerAnalytics'])->name('reports.customer-analytics');
    Route::get('reports/ecommerce-analytics', [\App\Http\Controllers\Admin\ReportController::class, 'ecommerceAnalytics'])->name('reports.ecommerce-analytics');
    Route::get('reports/combined-analytics', [\App\Http\Controllers\Admin\ReportController::class, 'combinedAnalytics'])->name('reports.combined-analytics');
    Route::get('reports/export-parcels', [\App\Http\Controllers\Admin\ReportController::class, 'exportParcels'])->name('reports.export-parcels');

    // Analytics
    Route::prefix('analytics')->name('analytics.')->group(function () {
        // Customer Analytics
        Route::get('customers', [\App\Http\Controllers\Admin\CustomerAnalyticsController::class, 'index'])->name('customers');
        Route::get('customers/growth-data', [\App\Http\Controllers\Admin\CustomerAnalyticsController::class, 'getCustomerGrowthData'])->name('customers.growth-data');
        Route::get('customers/revenue-segments', [\App\Http\Controllers\Admin\CustomerAnalyticsController::class, 'getRevenueByCustomerSegment'])->name('customers.revenue-segments');

        // Parcel Analytics
        Route::get('parcels', [\App\Http\Controllers\Admin\ParcelAnalyticsController::class, 'index'])->name('parcels');
        Route::get('parcels/trends-data', [\App\Http\Controllers\Admin\ParcelAnalyticsController::class, 'getParcelTrendsData'])->name('parcels.trends-data');
        Route::get('parcels/status-data', [\App\Http\Controllers\Admin\ParcelAnalyticsController::class, 'getStatusDistributionData'])->name('parcels.status-data');
        Route::get('parcels/revenue-data', [\App\Http\Controllers\Admin\ParcelAnalyticsController::class, 'getRevenueTrendsData'])->name('parcels.revenue-data');
    });

    // User Management
    Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
    Route::post('users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('users/{user}/update-role', [\App\Http\Controllers\Admin\UserController::class, 'updateRole'])->name('users.update-role');
    Route::post('users/{user}/reset-password', [\App\Http\Controllers\Admin\UserController::class, 'resetPassword'])->name('users.reset-password');
    Route::post('users/bulk-action', [\App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('users.bulk-action');

    // Customer Management
    Route::resource('customers', \App\Http\Controllers\Admin\CustomerController::class);
    Route::post('customers/{customer}/toggle-status', [\App\Http\Controllers\Admin\CustomerController::class, 'toggleStatus'])->name('customers.toggle-status');
    Route::get('customers/{customer}/orders', [\App\Http\Controllers\Admin\CustomerController::class, 'orders'])->name('customers.orders');
    Route::get('customers/{customer}/support-tickets', [\App\Http\Controllers\Admin\CustomerController::class, 'supportTickets'])->name('customers.support-tickets');
    Route::post('customers/bulk-action', [\App\Http\Controllers\Admin\CustomerController::class, 'bulkAction'])->name('customers.bulk-action');

    // Quote Management
    Route::resource('quotes', \App\Http\Controllers\Admin\QuoteController::class);
    Route::post('quotes/{quote}/provide-quote', [\App\Http\Controllers\Admin\QuoteController::class, 'provideQuote'])->name('quotes.provide-quote');
    Route::post('quotes/{quote}/update-status', [\App\Http\Controllers\Admin\QuoteController::class, 'updateStatus'])->name('quotes.update-status');
    Route::post('quotes/{quote}/assign', [\App\Http\Controllers\Admin\QuoteController::class, 'assign'])->name('quotes.assign');
    Route::post('quotes/bulk-action', [\App\Http\Controllers\Admin\QuoteController::class, 'bulkAction'])->name('quotes.bulk-action');

    // CMS Management
    Route::prefix('cms')->name('cms.')->group(function () {
        // Site Settings
        Route::get('settings', [\App\Http\Controllers\Admin\SiteSettingController::class, 'index'])->name('settings.index');
        Route::post('settings', [\App\Http\Controllers\Admin\SiteSettingController::class, 'update'])->name('settings.update');
        Route::post('settings/create', [\App\Http\Controllers\Admin\SiteSettingController::class, 'store'])->name('settings.store');
        Route::delete('settings/{siteSetting}', [\App\Http\Controllers\Admin\SiteSettingController::class, 'destroy'])->name('settings.destroy');
        Route::post('settings/reset', [\App\Http\Controllers\Admin\SiteSettingController::class, 'reset'])->name('settings.reset');

        // PayPal Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('paypal', [\App\Http\Controllers\Admin\PayPalSettingsController::class, 'index'])->name('paypal.index');
            Route::put('paypal', [\App\Http\Controllers\Admin\PayPalSettingsController::class, 'update'])->name('paypal.update');
            Route::post('paypal/test', [\App\Http\Controllers\Admin\PayPalSettingsController::class, 'testConnection'])->name('paypal.test');
            Route::get('paypal/status', [\App\Http\Controllers\Admin\PayPalSettingsController::class, 'status'])->name('paypal.status');
            Route::delete('paypal/reset', [\App\Http\Controllers\Admin\PayPalSettingsController::class, 'reset'])->name('paypal.reset');
        });

        // Team Members
        Route::resource('team', \App\Http\Controllers\Admin\TeamMemberController::class);
        Route::post('team/update-order', [\App\Http\Controllers\Admin\TeamMemberController::class, 'updateOrder'])->name('team.update-order');
        Route::post('team/{team}/toggle-status', [\App\Http\Controllers\Admin\TeamMemberController::class, 'toggleStatus'])->name('team.toggle-status');

        // Sliders
        Route::resource('sliders', \App\Http\Controllers\Admin\SliderController::class);
        Route::get('sliders-preview', [\App\Http\Controllers\Admin\SliderController::class, 'preview'])->name('sliders.preview');
        Route::get('sliders-preview-data', [\App\Http\Controllers\Admin\SliderController::class, 'previewData'])->name('sliders.preview-data');
        Route::post('sliders/update-order', [\App\Http\Controllers\Admin\SliderController::class, 'updateOrder'])->name('sliders.update-order');
        Route::post('sliders/{slider}/toggle-status', [\App\Http\Controllers\Admin\SliderController::class, 'toggleStatus'])->name('sliders.toggle-status');
        Route::post('sliders/{slider}/duplicate', [\App\Http\Controllers\Admin\SliderController::class, 'duplicate'])->name('sliders.duplicate');

        // Careers
        Route::resource('careers', \App\Http\Controllers\Admin\CareerController::class);
        Route::post('careers/{career}/toggle-status', [\App\Http\Controllers\Admin\CareerController::class, 'toggleStatus'])->name('careers.toggle-status');

        // Job Applications
        Route::resource('job-applications', \App\Http\Controllers\Admin\JobApplicationController::class)->except(['create', 'store']);
        Route::post('job-applications/{jobApplication}/update-status', [\App\Http\Controllers\Admin\JobApplicationController::class, 'updateStatus'])->name('job-applications.update-status');
        Route::post('job-applications/bulk-action', [\App\Http\Controllers\Admin\JobApplicationController::class, 'bulkAction'])->name('job-applications.bulk-action');
    });

    // Blog Management
    Route::prefix('blog')->name('blog.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\BlogController::class, 'index'])->name('index');
        Route::get('create', [\App\Http\Controllers\Admin\BlogController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\BlogController::class, 'store'])->name('store');
        Route::get('{blogPost}', [\App\Http\Controllers\Admin\BlogController::class, 'show'])->name('show');
        Route::get('{blogPost}/edit', [\App\Http\Controllers\Admin\BlogController::class, 'edit'])->name('edit');
        Route::put('{blogPost}', [\App\Http\Controllers\Admin\BlogController::class, 'update'])->name('update');
        Route::delete('{blogPost}', [\App\Http\Controllers\Admin\BlogController::class, 'destroy'])->name('destroy');
        Route::post('bulk-action', [\App\Http\Controllers\Admin\BlogController::class, 'bulkAction'])->name('bulk-action');
        Route::post('{blogPost}/toggle-featured', [\App\Http\Controllers\Admin\BlogController::class, 'toggleFeatured'])->name('toggle-featured');
    });

    // Communications Management
    Route::prefix('communications')->name('communications.')->group(function () {
        // Contact Management
        Route::resource('contacts', \App\Http\Controllers\Admin\ContactController::class)->except(['create', 'store', 'edit']);
        Route::post('contacts/{contact}/mark-read', [\App\Http\Controllers\Admin\ContactController::class, 'markAsRead'])->name('contacts.mark-read');
        Route::post('contacts/bulk-action', [\App\Http\Controllers\Admin\ContactController::class, 'bulkAction'])->name('contacts.bulk-action');

        // Newsletter Management
        Route::resource('newsletter', \App\Http\Controllers\Admin\NewsletterController::class);
        Route::post('newsletter/import', [\App\Http\Controllers\Admin\NewsletterController::class, 'import'])->name('newsletter.import');
        Route::post('newsletter/bulk-action', [\App\Http\Controllers\Admin\NewsletterController::class, 'bulkAction'])->name('newsletter.bulk-action');

        // Live Chat Management
        Route::prefix('live-chat')->name('live-chat.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\LiveChatController::class, 'index'])->name('index');
            Route::get('history', [\App\Http\Controllers\Admin\LiveChatController::class, 'history'])->name('history');
            Route::get('sessions/{session}', [\App\Http\Controllers\Admin\LiveChatController::class, 'show'])->name('show');
            Route::get('sessions/{session}/details', [\App\Http\Controllers\Admin\LiveChatController::class, 'getSessionDetails'])->name('session-details');
            Route::post('sessions/{session}/assign', [\App\Http\Controllers\Admin\LiveChatController::class, 'assign'])->name('assign');
            Route::post('sessions/{session}/send-message', [\App\Http\Controllers\Admin\LiveChatController::class, 'sendMessage'])->name('send-message');
            Route::get('sessions/{session}/new-messages', [\App\Http\Controllers\Admin\LiveChatController::class, 'getNewMessages'])->name('get-new-messages');
            Route::post('sessions/{session}/close', [\App\Http\Controllers\Admin\LiveChatController::class, 'close'])->name('close');
            Route::post('sessions/{session}/reopen', [\App\Http\Controllers\Admin\LiveChatController::class, 'reopen'])->name('reopen');
            Route::get('stats', [\App\Http\Controllers\Admin\LiveChatController::class, 'getStats'])->name('stats');
        });
    });

    // E-commerce Management
    // Category Management
    Route::prefix('ecommerce/categories')->name('categories.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('index');
        Route::get('create', [\App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\CategoryController::class, 'store'])->name('store');
        Route::get('{category}', [\App\Http\Controllers\Admin\CategoryController::class, 'show'])->name('show');
        Route::get('{category}/edit', [\App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('edit');
        Route::put('{category}', [\App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('update');
        Route::delete('{category}', [\App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('destroy');
        Route::post('{category}/toggle-status', [\App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('update-order', [\App\Http\Controllers\Admin\CategoryController::class, 'updateOrder'])->name('update-order');
        Route::get('api/tree', [\App\Http\Controllers\Admin\CategoryController::class, 'tree'])->name('tree');
    });

    // Product Management
    Route::prefix('ecommerce/products')->name('products.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\ProductController::class, 'index'])->name('index');

        // Multi-step product creation
        Route::get('create', [\App\Http\Controllers\Admin\ProductController::class, 'create'])->name('create');
        Route::post('create/step1', [\App\Http\Controllers\Admin\ProductController::class, 'storeStep1'])->name('create.step1.store');
        Route::get('create/step2', [\App\Http\Controllers\Admin\ProductController::class, 'createStep2'])->name('create.step2');
        Route::post('create/step2', [\App\Http\Controllers\Admin\ProductController::class, 'storeStep2'])->name('create.step2.store');
        Route::get('create/step3', [\App\Http\Controllers\Admin\ProductController::class, 'createStep3'])->name('create.step3');
        Route::post('create/step3', [\App\Http\Controllers\Admin\ProductController::class, 'storeStep3'])->name('create.step3.store');
        Route::get('create/step4', [\App\Http\Controllers\Admin\ProductController::class, 'createStep4'])->name('create.step4');
        Route::post('create/step4', [\App\Http\Controllers\Admin\ProductController::class, 'storeStep4'])->name('create.step4.store');

        Route::get('{product}', [\App\Http\Controllers\Admin\ProductController::class, 'show'])->name('show');
        Route::get('{product}/edit', [\App\Http\Controllers\Admin\ProductController::class, 'edit'])->name('edit');
        Route::put('{product}', [\App\Http\Controllers\Admin\ProductController::class, 'update'])->name('update');
        Route::delete('{product}', [\App\Http\Controllers\Admin\ProductController::class, 'destroy'])->name('destroy');
        Route::post('{product}/toggle-status', [\App\Http\Controllers\Admin\ProductController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('{product}/duplicate', [\App\Http\Controllers\Admin\ProductController::class, 'duplicate'])->name('duplicate');
        Route::get('{product}/analytics', [\App\Http\Controllers\Admin\ProductController::class, 'analytics'])->name('analytics');
        Route::post('bulk-action', [\App\Http\Controllers\Admin\ProductController::class, 'bulkAction'])->name('bulk-action');
    });

    // Order Management
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\OrderController::class, 'index'])->name('index');
        Route::get('create', [\App\Http\Controllers\Admin\OrderController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\OrderController::class, 'store'])->name('store');
        Route::get('{order}', [\App\Http\Controllers\Admin\OrderController::class, 'show'])->name('show');
        Route::get('{order}/edit', [\App\Http\Controllers\Admin\OrderController::class, 'edit'])->name('edit');
        Route::put('{order}', [\App\Http\Controllers\Admin\OrderController::class, 'update'])->name('update');
        Route::delete('{order}', [\App\Http\Controllers\Admin\OrderController::class, 'destroy'])->name('destroy');
        Route::post('{order}/update-status', [\App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('update-status');
        Route::post('{order}/update-payment-status', [\App\Http\Controllers\Admin\OrderController::class, 'updatePaymentStatus'])->name('update-payment-status');
        Route::post('bulk-update', [\App\Http\Controllers\Admin\OrderController::class, 'bulkUpdate'])->name('bulk-update');
    });

    // E-commerce Reports
    Route::prefix('ecommerce/reports')->name('ecommerce.reports.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'index'])->name('index');
        Route::get('product-analytics', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'productAnalytics'])->name('product-analytics');
        Route::get('sales-analytics', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'salesAnalytics'])->name('sales-analytics');
        Route::get('inventory-analytics', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'inventoryAnalytics'])->name('inventory-analytics');
        Route::get('export-products', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'exportProducts'])->name('export-products');
        Route::get('export-orders', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'exportOrders'])->name('export-orders');
        Route::get('export-analytics', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'exportAnalytics'])->name('export-analytics');
        Route::get('export-analytics-pdf', [\App\Http\Controllers\Admin\EcommerceReportController::class, 'exportAnalyticsPdf'])->name('export-analytics-pdf');
    });
});

// Cart Routes
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('index');
    Route::post('/add', [CartController::class, 'add'])->name('add');
    Route::post('/update', [CartController::class, 'update'])->name('update');
    Route::post('/remove', [CartController::class, 'remove'])->name('remove');
    Route::post('/clear', [CartController::class, 'clear'])->name('clear');
    Route::get('/count', [CartController::class, 'count'])->name('count');
    Route::post('/save-for-later', [CartController::class, 'saveForLater'])->name('save-for-later');
});

// Checkout Routes
Route::prefix('checkout')->name('checkout.')->group(function () {
    Route::get('/', [CheckoutController::class, 'index'])->middleware('customer')->name('index');
    Route::post('/process', [CheckoutController::class, 'process'])->name('process');
    Route::get('/confirmation/{order}', [CheckoutController::class, 'confirmation'])->middleware('customer')->name('confirmation');
});

// Address Management Routes
Route::prefix('addresses')->name('addresses.')->middleware('auth')->group(function () {
    Route::get('/', [AddressController::class, 'index'])->name('index');
    Route::get('/create', [AddressController::class, 'create'])->name('create');
    Route::post('/', [AddressController::class, 'store'])->name('store');
    Route::get('/{address}/edit', [AddressController::class, 'edit'])->name('edit');
    Route::put('/{address}', [AddressController::class, 'update'])->name('update');
    Route::delete('/{address}', [AddressController::class, 'destroy'])->name('destroy');
    Route::post('/{address}/set-default', [AddressController::class, 'setDefault'])->name('set-default');
    Route::get('/get-addresses', [AddressController::class, 'getAddresses'])->name('get-addresses');
});

// Enhanced Wishlist Routes (outside customer prefix for easier access)
Route::prefix('wishlist')->name('wishlist.')->middleware('auth')->group(function () {
    Route::post('/add', [\App\Http\Controllers\Customer\WishlistController::class, 'store'])->name('add');
    Route::post('/remove', [\App\Http\Controllers\Customer\WishlistController::class, 'destroy'])->name('remove');
});


require __DIR__.'/auth.php';
