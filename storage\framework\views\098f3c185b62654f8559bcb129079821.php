<?php $__env->startSection('title', 'Address Book'); ?>
<?php $__env->startSection('page-title', 'Address Book'); ?>

<?php $__env->startSection('page-actions'); ?>
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
    <i class="fas fa-plus me-2"></i>
    Add New Address
</button>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-address-book me-2"></i>
                    My Addresses
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Primary Address -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="fas fa-home me-2"></i>
                                    Primary Address
                                </span>
                                <span class="badge bg-light text-primary">Default</span>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title"><?php echo e($user->name); ?></h6>
                                <p class="card-text">
                                    <?php if($user->address): ?>
                                        <?php echo e($user->address); ?><br>
                                    <?php endif; ?>
                                    <?php if($user->city || $user->state || $user->postal_code): ?>
                                        <?php echo e($user->city); ?><?php if($user->city && $user->state): ?>, <?php endif; ?><?php echo e($user->state); ?> <?php echo e($user->postal_code); ?><br>
                                    <?php endif; ?>
                                    <?php if($user->country): ?>
                                        <?php echo e($user->country); ?>

                                    <?php endif; ?>
                                </p>
                                <?php if($user->phone): ?>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-phone me-2"></i>
                                        <?php echo e($user->phone); ?>

                                    </p>
                                <?php endif; ?>
                                <p class="text-muted mb-3">
                                    <i class="fas fa-envelope me-2"></i>
                                    <?php echo e($user->email); ?>

                                </p>
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editAddress('primary')">
                                        <i class="fas fa-edit me-1"></i>
                                        Edit
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional addresses would go here -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card border-dashed h-100 d-flex align-items-center justify-content-center" style="border: 2px dashed #dee2e6; min-height: 250px;">
                            <div class="text-center text-muted">
                                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                                <h6>Add New Address</h6>
                                <p class="small">Click to add a new shipping address</p>
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                    Add Address
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if(!$user->address && !$user->city && !$user->state): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Complete Your Profile:</strong> 
                        Add your address information to make shipping faster and easier.
                        <a href="<?php echo e(route('customer.profile.edit')); ?>" class="alert-link">Update Profile</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Address Modal -->
<div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAddressModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>
                    Add New Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addAddressForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <!-- Address Type -->
                    <div class="mb-3">
                        <label for="address_type" class="form-label">Address Type</label>
                        <select class="form-select" id="address_type" name="type" required>
                            <option value="shipping">Shipping</option>
                            <option value="billing">Billing</option>
                            <option value="both">Both</option>
                        </select>
                    </div>

                    <!-- Label -->
                    <div class="mb-3">
                        <label for="address_label" class="form-label">Address Label</label>
                        <input type="text" class="form-control" id="address_label" name="label" 
                               placeholder="e.g., Home, Office, etc.">
                    </div>

                    <!-- Full Address Form Component -->
                    <?php if (isset($component)) { $__componentOriginale37c8742b599dcb4f0150e043f89208d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale37c8742b599dcb4f0150e043f89208d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.address-form','data' => ['name' => 'address','style' => 'bootstrap','required' => true,'showAddressType' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('address-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'address','style' => 'bootstrap','required' => true,'showAddressType' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale37c8742b599dcb4f0150e043f89208d)): ?>
<?php $attributes = $__attributesOriginale37c8742b599dcb4f0150e043f89208d; ?>
<?php unset($__attributesOriginale37c8742b599dcb4f0150e043f89208d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale37c8742b599dcb4f0150e043f89208d)): ?>
<?php $component = $__componentOriginale37c8742b599dcb4f0150e043f89208d; ?>
<?php unset($__componentOriginale37c8742b599dcb4f0150e043f89208d); ?>
<?php endif; ?>

                    <!-- Default Address Option -->
                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="is_default" name="is_default">
                        <label class="form-check-label" for="is_default">
                            Set as default address
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Save Address
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Address Modal -->
<div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAddressModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    Edit Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAddressForm">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> This will update your primary profile address. 
                        <a href="<?php echo e(route('customer.profile.edit')); ?>" class="alert-link">Go to Profile Settings</a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="<?php echo e(route('customer.profile.edit')); ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        Edit in Profile
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Address Form
    const addAddressForm = document.getElementById('addAddressForm');
    if (addAddressForm) {
        addAddressForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // For now, show a message that this feature is coming soon
            alert('Address book functionality is coming soon! For now, you can update your primary address in Profile Settings.');

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addAddressModal'));
            modal.hide();
        });
    }
});

function editAddress(type) {
    if (type === 'primary') {
        // Show edit modal or redirect to profile edit
        const editModal = new bootstrap.Modal(document.getElementById('editAddressModal'));
        editModal.show();
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/customer/profile/address-book.blade.php ENDPATH**/ ?>