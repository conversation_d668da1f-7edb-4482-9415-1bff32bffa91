<?php

return [
    /*
    |--------------------------------------------------------------------------
    | PayPal Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains all PayPal-related configuration settings for the
    | logistics application. It supports both sandbox and live environments.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | PayPal Mode
    |--------------------------------------------------------------------------
    |
    | Set to 'sandbox' for testing or 'live' for production
    |
    */
    'mode' => env('PAYPAL_MODE', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | PayPal API Credentials
    |--------------------------------------------------------------------------
    |
    | Your PayPal API credentials. These are different for sandbox and live.
    |
    */
    'client_id' => env('PAYPAL_CLIENT_ID'),
    'client_secret' => env('PAYPAL_CLIENT_SECRET'),

    /*
    |--------------------------------------------------------------------------
    | PayPal Currency
    |--------------------------------------------------------------------------
    |
    | Default currency for PayPal transactions
    |
    */
    'currency' => env('PAYPAL_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | PayPal Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | Webhook ID for PayPal event notifications
    |
    */
    'webhook_id' => env('PAYPAL_WEBHOOK_ID'),

    /*
    |--------------------------------------------------------------------------
    | PayPal Sandbox Test Account
    |--------------------------------------------------------------------------
    |
    | Test account credentials for sandbox testing
    |
    */
    'sandbox' => [
        'username' => env('PAYPAL_SANDBOX_USERNAME', '<EMAIL>'),
        'password' => env('PAYPAL_SANDBOX_PASSWORD', 'Bm<2L2c%'),
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal URLs
    |--------------------------------------------------------------------------
    |
    | PayPal API and web URLs for different environments
    |
    */
    'urls' => [
        'sandbox' => [
            'api' => 'https://api-m.sandbox.paypal.com',
            'web' => 'https://www.sandbox.paypal.com',
        ],
        'live' => [
            'api' => 'https://api-m.paypal.com',
            'web' => 'https://www.paypal.com',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal Application Context
    |--------------------------------------------------------------------------
    |
    | Default settings for PayPal payment experience
    |
    */
    'application_context' => [
        'brand_name' => env('APP_NAME', 'Atrix Logistics'),
        'landing_page' => 'BILLING', // BILLING or LOGIN
        'user_action' => 'PAY_NOW', // PAY_NOW or CONTINUE
        'shipping_preference' => 'NO_SHIPPING', // NO_SHIPPING, GET_FROM_FILE, SET_PROVIDED_ADDRESS
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal Payment Settings
    |--------------------------------------------------------------------------
    |
    | Default payment configuration
    |
    */
    'payment' => [
        'intent' => 'CAPTURE', // CAPTURE or AUTHORIZE
        'auto_capture' => true,
        'timeout' => 30, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal Logging
    |--------------------------------------------------------------------------
    |
    | Enable/disable PayPal transaction logging
    |
    */
    'logging' => [
        'enabled' => env('PAYPAL_LOGGING', true),
        'level' => env('PAYPAL_LOG_LEVEL', 'info'), // debug, info, warning, error
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal Error Handling
    |--------------------------------------------------------------------------
    |
    | Error handling configuration
    |
    */
    'error_handling' => [
        'retry_attempts' => 3,
        'retry_delay' => 1000, // milliseconds
        'timeout' => 30, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | PayPal Features
    |--------------------------------------------------------------------------
    |
    | Enable/disable specific PayPal features
    |
    */
    'features' => [
        'webhooks' => env('PAYPAL_WEBHOOKS_ENABLED', false),
        'subscriptions' => env('PAYPAL_SUBSCRIPTIONS_ENABLED', false),
        'marketplace' => env('PAYPAL_MARKETPLACE_ENABLED', false),
    ],
];
