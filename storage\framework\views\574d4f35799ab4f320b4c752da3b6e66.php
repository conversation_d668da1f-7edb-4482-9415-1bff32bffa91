<?php $__env->startSection('title', 'Order Details'); ?>
<?php $__env->startSection('page-title', 'Order #' . $order->order_number); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('customer.orders')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Orders
        </a>
        <?php if($order->canBeTracked()): ?>
            <a href="<?php echo e(route('customer.track')); ?>?tracking_number=<?php echo e($order->tracking_number); ?>" 
               class="btn btn-primary">
                <i class="fas fa-map-marker-alt me-1"></i> Track Order
            </a>
        <?php endif; ?>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="#" onclick="printOrder()">
                        <i class="fas fa-print me-2"></i> Print Order
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="<?php echo e(route('customer.support.create')); ?>?order_number=<?php echo e($order->order_number); ?>">
                        <i class="fas fa-headset me-2"></i> Get Support
                    </a>
                </li>
                <?php if($order->canBeReordered()): ?>
                    <li>
                        <a class="dropdown-item" href="#" onclick="reorderItems()">
                            <i class="fas fa-redo me-2"></i> Reorder Items
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-8">
            <!-- Order Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Order Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Status</label>
                                <div>
                                    <span class="badge bg-<?php echo e($order->status_badge_color); ?> fs-6">
                                        <?php echo e($order->formatted_status); ?>

                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Payment Status</label>
                                <div>
                                    <span class="badge bg-<?php echo e($order->payment_status_badge_color); ?> fs-6">
                                        <?php echo e($order->formatted_payment_status); ?>

                                    </span>
                                </div>
                            </div>

                            <?php if($order->payment_method): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Payment Method</label>
                                    <p class="mb-0">
                                        <?php switch($order->payment_method):
                                            case ('manual'): ?>
                                                <i class="fas fa-money-bill-wave text-success me-2"></i>Manual Payment
                                                <?php break; ?>
                                            <?php case ('paypal'): ?>
                                                <i class="fab fa-paypal text-primary me-2"></i>PayPal
                                                <?php break; ?>
                                            <?php case ('stripe'): ?>
                                                <i class="fas fa-credit-card text-info me-2"></i>Credit/Debit Card
                                                <?php break; ?>
                                            <?php default: ?>
                                                <i class="fas fa-question-circle text-muted me-2"></i><?php echo e(ucfirst($order->payment_method)); ?>

                                        <?php endswitch; ?>
                                        <?php if($order->payment_reference): ?>
                                            <br><small class="text-muted">Reference: <?php echo e($order->payment_reference); ?></small>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Date</label>
                                <p class="mb-0">
                                    <?php echo e($order->created_at->format('M d, Y h:i A')); ?><br>
                                    <small class="text-muted"><?php echo e($order->created_at->diffForHumans()); ?></small>
                                </p>
                            </div>

                            <?php if($order->tracking_number): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Tracking Number</label>
                                    <p class="mb-0">
                                        <code><?php echo e($order->tracking_number); ?></code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo e($order->tracking_number); ?>')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Order Items (<?php echo e($order->items->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($item->product && $item->product->featured_image): ?>
                                                    <img src="<?php echo e(Storage::url($item->product->featured_image)); ?>" 
                                                         alt="<?php echo e($item->product_name); ?>" 
                                                         class="me-3" 
                                                         style="width: 60px; height: 60px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 60px; height: 60px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo e($item->product_name); ?></strong>
                                                    <?php if($item->product_description): ?>
                                                        <br><small class="text-muted"><?php echo e($item->product_description); ?></small>
                                                    <?php endif; ?>
                                                    <?php if($item->product_sku): ?>
                                                        <br><code class="small"><?php echo e($item->product_sku); ?></code>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo \App\Helpers\CurrencyHelper::format($item->unit_price); ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($item->quantity); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo \App\Helpers\CurrencyHelper::format($item->total_price); ?></strong>
                                        </td>
                                        <td>
                                            <?php if($item->product): ?>
                                                <a href="#" class="btn btn-sm btn-outline-primary" onclick="addToWishlist(<?php echo e($item->product_id); ?>)">
                                                    <i class="fas fa-heart"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-success" onclick="addToCart(<?php echo e($item->product_id); ?>)">
                                                    <i class="fas fa-cart-plus"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Shipping Address
                    </h6>
                </div>
                <div class="card-body">
                    <address class="mb-0">
                        <strong><?php echo e($order->shipping_first_name); ?> <?php echo e($order->shipping_last_name); ?></strong><br>
                        <?php if($order->shipping_company): ?>
                            <?php echo e($order->shipping_company); ?><br>
                        <?php endif; ?>
                        <?php echo e($order->shipping_address_1); ?><br>
                        <?php if($order->shipping_address_2): ?>
                            <?php echo e($order->shipping_address_2); ?><br>
                        <?php endif; ?>
                        <?php echo e($order->shipping_city); ?>, <?php echo e($order->shipping_state); ?> <?php echo e($order->shipping_postal_code); ?><br>
                        <?php echo e($order->shipping_country); ?>

                    </address>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Order Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span><?php echo \App\Helpers\CurrencyHelper::format($order->subtotal); ?></span>
                    </div>
                    
                    <?php if($order->shipping_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span><?php echo \App\Helpers\CurrencyHelper::format($order->shipping_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($order->tax_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span><?php echo \App\Helpers\CurrencyHelper::format($order->tax_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($order->discount_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Discount:</span>
                            <span>-<?php echo \App\Helpers\CurrencyHelper::format($order->discount_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong class="h5"><?php echo \App\Helpers\CurrencyHelper::format($order->total_amount); ?></strong>
                    </div>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Order Timeline
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Order Placed</h6>
                                <p class="mb-0 text-muted"><?php echo e($order->created_at->format('M d, Y h:i A')); ?></p>
                            </div>
                        </div>
                        
                        <?php if($order->paid_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Payment Confirmed</h6>
                                    <p class="mb-0 text-muted"><?php echo e($order->paid_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($order->shipped_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Shipped</h6>
                                    <p class="mb-0 text-muted"><?php echo e($order->shipped_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if($order->delivered_at): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Delivered</h6>
                                    <p class="mb-0 text-muted"><?php echo e($order->delivered_at->format('M d, Y h:i A')); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if($order->canBePaid()): ?>
                            <a href="<?php echo e(route('customer.orders.pay', $order)); ?>"
                               class="btn btn-success btn-lg">
                                <i class="fas fa-credit-card me-2"></i>
                                Pay Now - <?php echo \App\Helpers\CurrencyHelper::format($order->total_amount); ?>
                            </a>
                        <?php elseif($order->needsPayment()): ?>
                            <div class="alert alert-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Payment Required</strong><br>
                                <small>This order requires payment to proceed.</small>
                            </div>
                            <a href="<?php echo e(route('customer.orders.pay', $order)); ?>"
                               class="btn btn-warning">
                                <i class="fas fa-credit-card me-2"></i>
                                Complete Payment
                            </a>
                        <?php elseif($order->isPaid()): ?>
                            <div class="alert alert-success mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Payment Completed</strong><br>
                                <small>Paid on <?php echo e($order->paid_at->format('M d, Y')); ?></small>
                            </div>
                        <?php endif; ?>

                        <?php if($order->canBeTracked()): ?>
                            <a href="<?php echo e(route('customer.track')); ?>?tracking_number=<?php echo e($order->tracking_number); ?>"
                               class="btn btn-primary">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Track Order
                            </a>
                        <?php endif; ?>

                        <a href="<?php echo e(route('customer.support.create')); ?>?order_number=<?php echo e($order->order_number); ?>"
                           class="btn btn-outline-warning">
                            <i class="fas fa-headset me-2"></i>
                            Get Support
                        </a>

                        <?php if($order->canBeReordered()): ?>
                            <button type="button" class="btn btn-outline-success" onclick="reorderItems()">
                                <i class="fas fa-redo me-2"></i>
                                Reorder Items
                            </button>
                        <?php endif; ?>

                        <button type="button" class="btn btn-outline-secondary" onclick="printOrder()">
                            <i class="fas fa-print me-2"></i>
                            Print Order
                        </button>

                        <?php if($order->canBeCancelled()): ?>
                            <button type="button" class="btn btn-outline-danger" onclick="cancelOrder()">
                                <i class="fas fa-times me-2"></i>
                                Cancel Order
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content h6 {
        margin-bottom: 5px;
        font-size: 0.9rem;
    }
    
    .timeline-content p {
        font-size: 0.8rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        });
    }

    function printOrder() {
        window.print();
    }

    function addToWishlist(productId) {
        // Implementation for adding to wishlist
        alert('Add to wishlist functionality coming soon!');
    }

    function addToCart(productId) {
        // Implementation for adding to cart
        alert('Add to cart functionality coming soon!');
    }

    function reorderItems() {
        if (confirm('Are you sure you want to reorder all items from this order?')) {
            // Create a form to submit the reorder request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo e(route('customer.orders.reorder', $order)); ?>';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            // Add to body and submit
            document.body.appendChild(form);
            form.submit();
        }
    }

    function cancelOrder() {
        if (confirm('Are you sure you want to cancel this order?\n\nThis action cannot be undone. If you have already paid, a refund will be processed according to our refund policy.')) {
            // Create a form to submit the cancellation request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo e(route('customer.orders.cancel', $order)); ?>';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            // Add to body and submit
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/customer/order-details.blade.php ENDPATH**/ ?>