<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class PayPalConfigService
{
    /**
     * Get PayPal configuration
     */
    public static function getConfig(): array
    {
        return [
            'mode' => config('paypal.mode', 'sandbox'),
            'client_id' => config('paypal.client_id'),
            'client_secret' => config('paypal.client_secret'),
            'currency' => config('paypal.currency', 'USD'),
            'webhook_id' => config('paypal.webhook_id'),
        ];
    }

    /**
     * Check if PayPal is properly configured
     */
    public static function isConfigured(): bool
    {
        $config = self::getConfig();
        
        return !empty($config['client_id']) && !empty($config['client_secret']);
    }

    /**
     * Get PayPal mode (sandbox or live)
     */
    public static function getMode(): string
    {
        return config('paypal.mode', 'sandbox');
    }

    /**
     * Check if in sandbox mode
     */
    public static function isSandbox(): bool
    {
        return self::getMode() === 'sandbox';
    }

    /**
     * Check if in live mode
     */
    public static function isLive(): bool
    {
        return self::getMode() === 'live';
    }

    /**
     * Get PayPal API URL
     */
    public static function getApiUrl(): string
    {
        $mode = self::getMode();
        return config("paypal.urls.{$mode}.api");
    }

    /**
     * Get PayPal web URL
     */
    public static function getWebUrl(): string
    {
        $mode = self::getMode();
        return config("paypal.urls.{$mode}.web");
    }

    /**
     * Get PayPal client credentials
     */
    public static function getCredentials(): array
    {
        return [
            'client_id' => config('paypal.client_id'),
            'client_secret' => config('paypal.client_secret'),
        ];
    }

    /**
     * Get PayPal sandbox test account
     */
    public static function getSandboxAccount(): array
    {
        return [
            'username' => config('paypal.sandbox.username'),
            'password' => config('paypal.sandbox.password'),
        ];
    }

    /**
     * Get PayPal application context
     */
    public static function getApplicationContext(): array
    {
        return config('paypal.application_context', []);
    }

    /**
     * Get PayPal payment settings
     */
    public static function getPaymentSettings(): array
    {
        return config('paypal.payment', []);
    }

    /**
     * Validate PayPal configuration
     */
    public static function validateConfig(): array
    {
        $errors = [];
        $config = self::getConfig();

        if (empty($config['client_id'])) {
            $errors[] = 'PayPal Client ID is required';
        }

        if (empty($config['client_secret'])) {
            $errors[] = 'PayPal Client Secret is required';
        }

        if (!in_array($config['mode'], ['sandbox', 'live'])) {
            $errors[] = 'PayPal mode must be either "sandbox" or "live"';
        }

        if (empty($config['currency'])) {
            $errors[] = 'PayPal currency is required';
        }

        return $errors;
    }

    /**
     * Log PayPal configuration status
     */
    public static function logConfigStatus(): void
    {
        $config = self::getConfig();
        $isConfigured = self::isConfigured();
        $mode = self::getMode();

        Log::info('PayPal Configuration Status', [
            'configured' => $isConfigured,
            'mode' => $mode,
            'has_client_id' => !empty($config['client_id']),
            'has_client_secret' => !empty($config['client_secret']),
            'currency' => $config['currency'],
        ]);

        if (!$isConfigured) {
            Log::warning('PayPal is not properly configured', [
                'missing_client_id' => empty($config['client_id']),
                'missing_client_secret' => empty($config['client_secret']),
            ]);
        }
    }

    /**
     * Get environment-specific configuration
     */
    public static function getEnvironmentConfig(): array
    {
        $mode = self::getMode();
        
        return [
            'mode' => $mode,
            'api_url' => self::getApiUrl(),
            'web_url' => self::getWebUrl(),
            'is_sandbox' => self::isSandbox(),
            'is_live' => self::isLive(),
        ];
    }

    /**
     * Get PayPal configuration for frontend
     */
    public static function getFrontendConfig(): array
    {
        return [
            'mode' => self::getMode(),
            'currency' => config('paypal.currency', 'USD'),
            'client_id' => config('paypal.client_id'), // Only client_id is safe for frontend
        ];
    }

    /**
     * Update PayPal configuration (for admin panel)
     */
    public static function updateConfig(array $config): bool
    {
        try {
            // Validate required fields
            $required = ['client_id', 'client_secret', 'mode'];
            foreach ($required as $field) {
                if (empty($config[$field])) {
                    throw new \InvalidArgumentException("Field {$field} is required");
                }
            }

            // Update configuration
            Config::set('paypal.client_id', $config['client_id']);
            Config::set('paypal.client_secret', $config['client_secret']);
            Config::set('paypal.mode', $config['mode']);
            
            if (isset($config['currency'])) {
                Config::set('paypal.currency', $config['currency']);
            }
            
            if (isset($config['webhook_id'])) {
                Config::set('paypal.webhook_id', $config['webhook_id']);
            }

            Log::info('PayPal configuration updated', [
                'mode' => $config['mode'],
                'currency' => $config['currency'] ?? 'USD',
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update PayPal configuration', [
                'error' => $e->getMessage(),
                'config' => array_keys($config),
            ]);

            return false;
        }
    }
}
