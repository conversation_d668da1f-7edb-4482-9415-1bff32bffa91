On the product details page, the prices for products whose category have been set to hide prices is still showing the prices. 
On the product catalog page, the feature to not display products works perfectly, check how it is implemented. 
The thing is admins can hide the prices of a certain category and on the public pages, the prices are hidden and displayed as +Quote instead of Add to Cart. 
So when the product does not have a price, on the product's detail page, it should be +Quote instead of Add to Cart. Check how this is implemented on the products page. There is already a function, use it. 
