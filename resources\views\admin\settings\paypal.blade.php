@extends('layouts.admin')

@section('title', 'PayPal Settings')
@section('page-title', 'PayPal Configuration')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Configuration Status Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fab fa-paypal me-2"></i>
                        PayPal Status
                    </h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="testConnection()">
                            <i class="fas fa-plug me-1"></i> Test Connection
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshStatus()">
                            <i class="fas fa-sync me-1"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="status-indicator me-3">
                                    @if($isConfigured)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i> Configured
                                        </span>
                                    @else
                                        <span class="badge bg-danger">
                                            <i class="fas fa-exclamation-circle me-1"></i> Not Configured
                                        </span>
                                    @endif
                                </div>
                                <div>
                                    <strong>Configuration Status</strong>
                                    <br>
                                    <small class="text-muted">
                                        Mode: <span class="badge bg-{{ $environmentConfig['is_sandbox'] ? 'warning' : 'success' }}">
                                            {{ strtoupper($environmentConfig['mode']) }}
                                        </span>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <i class="fas fa-globe fa-2x text-primary"></i>
                                </div>
                                <div>
                                    <strong>Environment</strong>
                                    <br>
                                    <small class="text-muted">{{ $environmentConfig['api_url'] }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(!empty($validationErrors))
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Configuration Issues:</h6>
                            <ul class="mb-0">
                                @foreach($validationErrors as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div id="connection-status" class="mt-3"></div>
                </div>
            </div>

            <!-- PayPal Configuration Form -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        PayPal Configuration
                    </h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="resetSettings()">
                            <i class="fas fa-undo me-1"></i> Reset to Default
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.settings.paypal.update') }}" method="POST" id="paypal-form">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="mode" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>
                                        PayPal Mode <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('mode') is-invalid @enderror" 
                                            id="mode" name="mode" required>
                                        <option value="sandbox" {{ ($config['mode'] ?? 'sandbox') === 'sandbox' ? 'selected' : '' }}>
                                            Sandbox (Testing)
                                        </option>
                                        <option value="live" {{ ($config['mode'] ?? '') === 'live' ? 'selected' : '' }}>
                                            Live (Production)
                                        </option>
                                    </select>
                                    @error('mode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Use Sandbox for testing, Live for production
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        Currency <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select @error('currency') is-invalid @enderror" 
                                            id="currency" name="currency" required>
                                        <option value="USD" {{ ($config['currency'] ?? 'USD') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                        <option value="EUR" {{ ($config['currency'] ?? '') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                        <option value="GBP" {{ ($config['currency'] ?? '') === 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                        <option value="CAD" {{ ($config['currency'] ?? '') === 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                        <option value="AUD" {{ ($config['currency'] ?? '') === 'AUD' ? 'selected' : '' }}>AUD - Australian Dollar</option>
                                    </select>
                                    @error('currency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">
                                        <i class="fas fa-key me-1"></i>
                                        Client ID <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control @error('client_id') is-invalid @enderror" 
                                           id="client_id" 
                                           name="client_id" 
                                           value="{{ old('client_id', $config['client_id'] ?? '') }}" 
                                           required
                                           placeholder="Enter your PayPal Client ID">
                                    @error('client_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="client_secret" class="form-label">
                                        <i class="fas fa-lock me-1"></i>
                                        Client Secret <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control @error('client_secret') is-invalid @enderror" 
                                           id="client_secret" 
                                           name="client_secret" 
                                           value="{{ old('client_secret', $config['client_secret'] ?? '') }}" 
                                           required
                                           placeholder="Enter your PayPal Client Secret">
                                    @error('client_secret')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="webhook_id" class="form-label">
                                        <i class="fas fa-webhook me-1"></i>
                                        Webhook ID (Optional)
                                    </label>
                                    <input type="text" 
                                           class="form-control @error('webhook_id') is-invalid @enderror" 
                                           id="webhook_id" 
                                           name="webhook_id" 
                                           value="{{ old('webhook_id', $config['webhook_id'] ?? '') }}" 
                                           placeholder="Enter your PayPal Webhook ID">
                                    @error('webhook_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Required for webhook event verification
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                <i class="fas fa-arrow-left me-1"></i> Back
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sandbox Test Account Info -->
            @if($environmentConfig['is_sandbox'])
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-vial me-2"></i>
                        Sandbox Test Account
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Test Account Credentials:</h6>
                        <p class="mb-2"><strong>Username:</strong> {{ $sandboxAccount['username'] }}</p>
                        <p class="mb-0"><strong>Password:</strong> {{ $sandboxAccount['password'] }}</p>
                    </div>
                    <p class="text-muted">
                        Use these credentials to test PayPal payments in sandbox mode. 
                        You can log into the PayPal sandbox at 
                        <a href="https://www.sandbox.paypal.com" target="_blank">sandbox.paypal.com</a>
                    </p>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function testConnection() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Testing...';

    fetch('{{ route("admin.settings.paypal.test") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        const statusDiv = document.getElementById('connection-status');

        if (data.success) {
            statusDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                    <div class="mt-2">
                        <small>Mode: ${data.config.mode} | Client ID: ${data.config.client_id_preview}</small>
                    </div>
                </div>
            `;
        } else {
            statusDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${data.message}
                </div>
            `;
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            statusDiv.innerHTML = '';
        }, 5000);
    })
    .catch(error => {
        document.getElementById('connection-status').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Connection test failed: ${error.message}
            </div>
        `;
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function refreshStatus() {
    fetch('{{ route("admin.settings.paypal.status") }}')
    .then(response => response.json())
    .then(data => {
        // Update status indicators
        location.reload(); // Simple refresh for now
    })
    .catch(error => {
        console.error('Failed to refresh status:', error);
    });
}

function resetSettings() {
    if (confirm('Are you sure you want to reset PayPal settings to default values? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.settings.paypal.reset") }}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

// Form validation
document.getElementById('paypal-form').addEventListener('submit', function(e) {
    const clientId = document.getElementById('client_id').value.trim();
    const clientSecret = document.getElementById('client_secret').value.trim();

    if (!clientId || !clientSecret) {
        e.preventDefault();
        alert('Please fill in both Client ID and Client Secret');
        return false;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Saving...';
});
</script>
@endpush
