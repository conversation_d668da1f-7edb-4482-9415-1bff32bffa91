<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\PayPalConfigService;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PayPalSettingsController extends Controller
{
    /**
     * Display PayPal settings
     */
    public function index()
    {
        $config = PayPalConfigService::getConfig();
        $isConfigured = PayPalConfigService::isConfigured();
        $validationErrors = PayPalConfigService::validateConfig();
        $environmentConfig = PayPalConfigService::getEnvironmentConfig();
        $sandboxAccount = PayPalConfigService::getSandboxAccount();

        return view('admin.settings.paypal', compact(
            'config',
            'isConfigured',
            'validationErrors',
            'environmentConfig',
            'sandboxAccount'
        ));
    }

    /**
     * Update PayPal settings
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'client_id' => 'required|string',
            'client_secret' => 'required|string',
            'mode' => 'required|in:sandbox,live',
            'currency' => 'required|string|size:3',
            'webhook_id' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update environment variables (this would typically require a more sophisticated approach)
            $settings = [
                'PAYPAL_CLIENT_ID' => $request->client_id,
                'PAYPAL_CLIENT_SECRET' => $request->client_secret,
                'PAYPAL_MODE' => $request->mode,
                'PAYPAL_CURRENCY' => $request->currency,
                'PAYPAL_WEBHOOK_ID' => $request->webhook_id,
            ];

            // Store in site settings as backup
            foreach ($settings as $key => $value) {
                SiteSetting::updateOrCreate(
                    ['key_name' => strtolower(str_replace('PAYPAL_', 'paypal_', $key))],
                    ['value' => $value]
                );
            }

            // Update the .env file (basic implementation)
            $this->updateEnvFile($settings);

            return redirect()->back()->with('success', 'PayPal settings updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update PayPal settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Test PayPal connection
     */
    public function testConnection()
    {
        try {
            $config = PayPalConfigService::getConfig();
            
            if (!PayPalConfigService::isConfigured()) {
                return response()->json([
                    'success' => false,
                    'message' => 'PayPal is not properly configured'
                ]);
            }

            // Try to create a PayPal client to test credentials
            $paypalService = app(\App\Services\PayPalPaymentService::class);
            
            return response()->json([
                'success' => true,
                'message' => 'PayPal connection successful!',
                'config' => [
                    'mode' => $config['mode'],
                    'currency' => $config['currency'],
                    'client_id_preview' => substr($config['client_id'], 0, 10) . '...',
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'PayPal connection failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get PayPal configuration status
     */
    public function status()
    {
        $config = PayPalConfigService::getConfig();
        $isConfigured = PayPalConfigService::isConfigured();
        $validationErrors = PayPalConfigService::validateConfig();

        return response()->json([
            'configured' => $isConfigured,
            'mode' => $config['mode'],
            'currency' => $config['currency'],
            'errors' => $validationErrors,
            'environment' => PayPalConfigService::getEnvironmentConfig(),
        ]);
    }

    /**
     * Update .env file with new settings
     */
    private function updateEnvFile(array $settings)
    {
        $envFile = base_path('.env');
        
        if (!file_exists($envFile)) {
            throw new \Exception('.env file not found');
        }

        $envContent = file_get_contents($envFile);

        foreach ($settings as $key => $value) {
            $pattern = "/^{$key}=.*$/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                // Update existing line
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                // Add new line
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }

    /**
     * Reset PayPal settings to default
     */
    public function reset()
    {
        try {
            $defaultSettings = [
                'PAYPAL_MODE' => 'sandbox',
                'PAYPAL_CLIENT_ID' => '',
                'PAYPAL_CLIENT_SECRET' => '',
                'PAYPAL_CURRENCY' => 'USD',
                'PAYPAL_WEBHOOK_ID' => '',
            ];

            $this->updateEnvFile($defaultSettings);

            // Remove from site settings
            SiteSetting::whereIn('key_name', [
                'paypal_client_id',
                'paypal_client_secret',
                'paypal_mode',
                'paypal_currency',
                'paypal_webhook_id'
            ])->delete();

            return redirect()->back()->with('success', 'PayPal settings reset to default values.');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to reset PayPal settings: ' . $e->getMessage());
        }
    }
}
